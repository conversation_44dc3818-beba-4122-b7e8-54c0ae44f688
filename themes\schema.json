{"$schema": "http://json-schema.org/draft-07/schema", "$id": "https://raw.githubusercontent.com/JanDeDobbeleer/oh-my-posh/main/themes/schema.json", "type": "object", "title": "The Oh My Posh theme definition", "description": "https://ohmyposh.dev/docs/configuration/general", "definitions": {"color": {"anyOf": [{"$ref": "#/definitions/color_string"}, {"$ref": "#/definitions/palette_reference"}]}, "color_string": {"type": "string", "pattern": "^(#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})|^([0-9]|[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])$|black|red|green|yellow|blue|magenta|cyan|white|default|darkGray|lightRed|lightGreen|lightYellow|lightBlue|lightMagenta|lightCyan|lightWhite|transparent|parentBackground|parentForeground|background|foreground|accent)$", "title": "Color string", "description": "https://ohmyposh.dev/docs/configuration/colors", "format": "color"}, "palette_reference": {"type": "string", "pattern": "^p:.*$", "title": "Palette reference", "description": "https://ohmyposh.dev/docs/configuration/colors#palette"}, "templates": {"type": "array", "title": "An array of templates", "default": [], "items": {"$ref": "#/definitions/segment/properties/template"}}, "home_enabled": {"type": "boolean", "title": "Enable in the HOME folder", "description": "Display the segment in the HOME folder", "default": false}, "fetch_version": {"type": "boolean", "title": "Fetch Version", "description": "Fetch the version number", "default": true}, "http_timeout": {"type": "integer", "title": "Http request timeout", "description": "Milliseconds to use for http request timeouts", "default": 20}, "expires_in": {"type": "integer", "title": "Expires in", "description": "Access token expiration time in seconds", "default": 0}, "access_token": {"type": "string", "title": "Access token", "description": "The initial access token", "default": ""}, "refresh_token": {"type": "string", "title": "Refresh token", "description": "The initial refresh token", "default": ""}, "display_mode": {"type": "string", "title": "Display Mode", "description": "Determines whether the segment is displayed always or only if a file matching the extensions are present in the current folder", "enum": ["always", "files", "environment", "context"], "default": "context"}, "missing_command_text": {"type": "string", "title": "Missing command text", "description": "The string to display when the command is not available", "default": ""}, "version_url_template": {"type": "string", "title": "Version Url Template", "description": "Template that creates the URL of the version info / release notes", "default": ""}, "status_formats": {"type": "object", "title": "Status string formats", "description": "Override the status format for a specific change. Example: {\"Added\": \"Added: %d\"}", "default": {}}, "folders": {"type": "array", "title": "Folders", "description": "The folders to look for when determining if a folder is a workspace", "default": [], "items": {"type": "string"}}, "native_fallback": {"type": "boolean", "title": "Native Fallback", "description": "Try to use the WSL 2 native command in a shared Windows drive if the Windows executable is not found.", "default": false}, "branch_template": {"type": "string", "title": "Branch template", "description": "the temaplate to use for the branch name, supports {{ .Branch }} for the branch name", "default": ""}, "mapped_branches": {"type": "object", "title": "Mapped Branches", "description": "Custom glyph/text for specific branches", "default": {}}, "cache_duration": {"type": "string", "title": "Cache duration", "description": "The duration for which the segment will be cached. This is parsed using the `time.ParseDuration` function from the Go standard library (see https://pkg.go.dev/time#ParseDuration for details).", "pattern": "^(none|infinite|([0-9]+(h|m|s))+)$"}, "filler": {"type": "string", "title": "Filler", "description": "Right aligned filler text, will span the remaining width."}, "aliases": {"type": "object", "title": "Aliases", "description": "Custom value replacement for template parts", "default": {}}, "extra_prompt": {"type": "object", "default": {}, "properties": {"template": {"type": "string", "title": "Prompt Template"}, "foreground": {"$ref": "#/definitions/color"}, "foreground_templates": {"$ref": "#/definitions/templates", "description": "https://ohmyposh.dev/docs/configuration/colors#color-templates"}, "background": {"$ref": "#/definitions/color"}, "background_templates": {"$ref": "#/definitions/templates", "description": "https://ohmyposh.dev/docs/configuration/colors#color-templates"}}}, "block": {"type": "object", "description": "https://ohmyposh.dev/docs/configuration/block", "allOf": [{"if": {"properties": {"type": {"const": "prompt"}}}, "then": {"required": ["type", "alignment", "segments"], "title": "Prompt definition, contains 1 or more segments to render"}}, {"if": {"properties": {"type": {"const": "rprompt"}}}, "then": {"required": ["type", "segments"], "title": "RPrompt definition, contains 1 or more segments to render to the right of the cursor"}}, {"if": {"properties": {"type": {"const": "prompt"}, "alignment": {"const": "right"}}}, "then": {"properties": {"overflow": {"type": "string", "title": "Block overflow", "description": "https://ohmyposh.dev/docs/configuration/block#overflow", "enum": ["break", "hide"], "default": ""}, "filler": {"$ref": "#/definitions/filler", "description": "https://ohmyposh.dev/docs/configuration/block#filler"}}}}], "properties": {"type": {"type": "string", "title": "Block type", "description": "https://ohmyposh.dev/docs/configuration/block#type", "enum": ["prompt", "rprompt"], "default": "prompt"}, "alignment": {"type": "string", "title": "Block alignment", "description": "https://ohmyposh.dev/docs/configuration/block#alignment", "enum": ["left", "right"], "default": "left"}, "newline": {"type": "boolean", "title": "Newline", "description": "https://ohmyposh.dev/docs/configuration/block#newline", "default": false}, "leading_diamond": {"type": "string", "title": "Leading diamond", "description": "https://ohmyposh.dev/docs/configuration/block#leading-diamond", "default": ""}, "trailing_diamond": {"type": "string", "title": "Trailing diamond", "description": "https://ohmyposh.dev/docs/configuration/block#trailing-diamond", "default": ""}, "segments": {"type": "array", "title": "Segments list, prompt elements to display based on context", "description": "https://ohmyposh.dev/docs/configuration/block#segments", "default": [], "items": {"$ref": "#/definitions/segment"}}}}, "segment": {"type": "object", "title": "Segment", "description": "https://ohmyposh.dev/docs/configuration/segment", "default": {}, "required": ["type", "style"], "properties": {"type": {"type": "string", "title": "Segment Type", "description": "https://ohmyposh.dev/docs/configuration/segment", "enum": ["angular", "argocd", "aurelia", "aws", "az", "azd", "azfunc", "battery", "bazel", "brewfather", "buf", "bun", "carbonintensity", "cds", "cf", "cftarget", "cmake", "command", "connection", "crystal", "dart", "deno", "docker", "dotnet", "elixir", "executiontime", "firebase", "flutter", "fortran", "fossil", "gcp", "git", "gitversion", "go", "haskell", "helm", "http", "ipify", "java", "jujutsu", "julia", "kotlin", "kubectl", "lastfm", "lua", "mercurial", "mojo", "mvn", "nbgv", "nightscout", "nim", "nix-shell", "node", "npm", "nx", "ocaml", "os", "owm", "path", "perl", "php", "plastic", "pnpm", "project", "pulumi", "python", "quasar", "r", "react", "root", "ruby", "rust", "sapling", "session", "shell", "sitecore", "spotify", "status", "strava", "svelte", "svn", "swift", "sysinfo", "talosctl", "tauri", "terraform", "text", "time", "ui5tooling", "umbraco", "unity", "upgrade", "v", "vala", "wakatime", "winreg", "withings", "xmake", "yarn", "ytm", "zig"]}, "style": {"title": "Segment Style", "description": "https://ohmyposh.dev/docs/configuration/segment#style", "anyOf": [{"enum": ["plain", "powerline", "diamond", "accordion"]}, {"type": "string"}]}, "foreground": {"$ref": "#/definitions/color"}, "foreground_templates": {"$ref": "#/definitions/templates", "description": "https://ohmyposh.dev/docs/configuration/colors#color-templates"}, "background": {"$ref": "#/definitions/color"}, "background_templates": {"$ref": "#/definitions/templates", "description": "https://ohmyposh.dev/docs/configuration/colors#color-templates"}, "template": {"type": "string", "title": "Template text", "description": "https://ohmyposh.dev/docs/configuration/templates", "default": ""}, "templates_logic": {"type": "string", "title": "Templates Logic", "description": "https://ohmyposh.dev/docs/configuration/segment", "enum": ["first_match", "join"]}, "max_width": {"type": "integer", "title": "if the terminal width exceeds this value, the segment will be hidden", "description": "https://ohmyposh.dev/docs/configuration/segment", "default": 0}, "min_width": {"type": "integer", "title": "if the terminal width is inferior than this value, the segment will be hidden", "description": "https://ohmyposh.dev/docs/configuration/segment", "default": 0}, "properties": {"type": "object", "title": "Segment Properties, used to change behavior/displaying", "description": "https://ohmyposh.dev/docs/configuration/segment#properties", "default": {}}, "interactive": {"type": "boolean", "title": "Allow the use of interactive prompt escape sequences", "description": "https://ohmyposh.dev/docs/configuration/segment", "default": false}, "alias": {"type": "string", "title": "Give the segment an alias for use in templates", "description": "https://ohmyposh.dev/docs/configuration/segment", "default": ""}, "include_folders": {"type": "array", "title": "If specified, segment will only render in these folders", "description": "https://ohmyposh.dev/docs/configuration/segment#include--exclude-folders", "default": [], "items": {"type": "string"}}, "exclude_folders": {"type": "array", "title": "Exclude rendering in these folders", "description": "https://ohmyposh.dev/docs/configuration/segment#include--exclude-folders", "default": [], "items": {"type": "string"}}, "cache": {"type": "object", "title": "Cache settings", "description": "https://ohmyposh.dev/docs/configuration/segment#cache", "default": {}, "properties": {"duration": {"$ref": "#/definitions/cache_duration"}, "strategy": {"type": "string", "title": "Cache strategy", "description": "https://ohmyposh.dev/docs/configuration/segment#strategy", "default": "folder", "enum": ["folder", "session"]}}}}, "allOf": [{"if": {"properties": {"type": {"const": "angular"}}}, "then": {"title": "Angular CLI Segment", "description": "https://ohmyposh.dev/docs/segments/cli/angular", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if the current directory is an Angular project", "default": ["angular.json"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "aurelia"}}}, "then": {"title": "Aurelia Segment", "description": "https://ohmyposh.dev/docs/segments/cli/aurelia", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if the current directory is an Aurelia project", "default": ["package.json"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "aws"}}}, "then": {"title": "AWS Segment", "description": "https://ohmyposh.dev/docs/segments/cloud/aws", "properties": {"properties": {"properties": {"display_default": {"type": "boolean", "title": "Display Default User Profile", "description": "Display the segment when default user or not", "default": true}}}}}}, {"if": {"properties": {"type": {"const": "az"}}}, "then": {"title": "Azure Segment", "description": "https://ohmyposh.dev/docs/segments/cloud/az", "properties": {"properties": {"properties": {"source": {"type": "string", "title": "Source", "description": "https://ohmyposh.dev/docs/segments/cloud/az#properties", "default": "first_match", "enum": ["cli", "pwsh"]}}}}}}, {"if": {"properties": {"type": {"const": "azd"}}}, "then": {"title": "Azure Developer CLI Segment", "description": "https://ohmyposh.dev/docs/segments/cloud/azd"}}, {"if": {"properties": {"type": {"const": "azfunc"}}}, "then": {"title": "Azure Function Segment", "description": "https://ohmyposh.dev/docs/segments/cloud/azfunc", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}}}}}}, {"if": {"properties": {"type": {"const": "battery"}}}, "then": {"title": "Battery Segment", "description": "https://ohmyposh.dev/docs/segments/system/battery", "properties": {"properties": {"properties": {"display_error": {"type": "boolean", "title": "<PERSON><PERSON><PERSON>rror", "description": "Show the error context when failing to retrieve the battery information", "default": false}, "charging_icon": {"type": "string", "title": "Charging Icon", "description": "Text/icon to display when charging", "default": ""}, "discharging_icon": {"type": "string", "title": "discharging Dcon", "description": "Text/icon to display when discharging", "default": ""}, "charged_icon": {"type": "string", "title": "Charged Icon", "description": "Text/icon to display when fully charged", "default": ""}, "not_charging_icon": {"type": "string", "title": "Not Charging Icon", "description": "Text/icon to display when on AC power", "default": ""}}}}}}, {"if": {"properties": {"type": {"const": "bazel"}}}, "then": {"title": "Bazel Segment", "description": "https://ohmyposh.dev/docs/segments/cli/bazel", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "icon": {"type": "string", "title": "Icon", "description": "The icon representing <PERSON><PERSON>'s logo", "default": ""}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a Bazel workspace", "default": ["*.bazel", "*.bzl", "BUILD", "WORKSPACE", ".bazel<PERSON>", ".bazel<PERSON>"], "items": {"type": "string"}}, "folders": {"type": "array", "title": "Folders", "description": "The folders to look for when determining if a folder is a Bazel workspace", "default": ["bazel-bin", "bazel-out", "bazel-testlogs"], "items": {"type": "string"}}}}}}}, {"if": {"properties": {"type": {"const": "brewfather"}}}, "then": {"title": "<PERSON><PERSON><PERSON>ch <PERSON>", "description": "https://ohmyposh.dev/docs/segments/web/brewfather", "properties": {"properties": {"properties": {"user_id": {"type": "string", "title": "Brewfather UserID (required)", "description": "Provided by Brewfather's Generate API Key settings option", "default": ""}, "api_key": {"type": "string", "title": "Brewfather API Key (required)", "description": "Provided by Brewfather's Generate API Key settings option", "default": ""}, "batch_id": {"type": "string", "title": "ID of the batch in Brewfather (required)", "description": "At the end of the URL when viewing the batch on the Brewfather site", "default": ""}, "day_icon": {"type": "string", "title": "Icon to use to indicate days", "description": "Appended to a number to indicate days, e.g. 25d", "default": "d"}, "http_timeout": {"$ref": "#/definitions/http_timeout"}, "doubleup_icon": {"type": "string", "title": "Temperature trend icon, very high positive change", "description": "Delta between this and prior temperature reading is very high (> 4C by default), available intemplate as .TemperatureTrend", "default": "↑↑"}, "singleup_icon": {"type": "string", "title": "Temperature trend icon, high positive change", "description": "Delta between this and prior temperature reading is high (2C < delta < 4C by default), available intemplate as .TemperatureTrend", "default": "↑"}, "fortyfiveup_icon": {"type": "string", "title": "Temperature trend icon, positive change", "description": "Delta between this and prior temperature reading is positive (0.5C < delta < 2C by default), available intemplate as .TemperatureTrend", "default": "↗"}, "flat_icon": {"type": "string", "title": "Temperature trend icon, flat/small change", "description": "Delta between this and prior temperature and this temperature reading (< +-0.5C change), available intemplate as .TemperatureTrend", "default": "→"}, "fortyfivedown_icon": {"type": "string", "title": "Temperature trend icon, v. negative change", "description": "Delta between this and prior temperature reading is negative (-0.5C > delta > -2C by default), available intemplate as .TemperatureTrend", "default": "↘"}, "singledown_icon": {"type": "string", "title": "Temperature trend icon, high negative change", "description": "Delta between this and prior temperature reading is large negative (-2C > delta > -4C by default), available intemplate as .TemperatureTrend", "default": "↓"}, "doubledown_icon": {"type": "string", "title": "Temperature trend icon, very high negative change", "description": "Delta between this and prior temperature reading is very large negative (> -4C by default), available intemplate as .TemperatureTrend", "default": "↓↓"}, "planning_status_icon": {"type": "string", "title": "Icon for batch in planning", "description": "Available in template as .StatusIcon", "default": ""}, "brewing_status_icon": {"type": "string", "title": "Icon for batch being brewed", "description": "Available in template as .StatusIcon", "default": ""}, "fermenting_status_icon": {"type": "string", "title": "Icon for batch fermenting", "description": "Available in template as .StatusIcon", "default": ""}, "conditioning_status_icon": {"type": "string", "title": "Icon for batch conditioning", "description": "Available in template as .StatusIcon", "default": ""}, "completed_status_icon": {"type": "string", "title": "Icon for completed batch", "description": "Available in template as .StatusIcon", "default": ""}, "archived_status_icon": {"type": "string", "title": "Icon for archived batch", "description": "Available in template as .StatusIcon", "default": ""}}}}}}, {"if": {"properties": {"type": {"const": "buf"}}}, "then": {"title": "Buf Segment", "description": "https://ohmyposh.dev/docs/segments/cli/buf", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a Buf workspace", "default": ["buf.yaml", "buf.gen.yaml", "buf.work.yaml"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "bun"}}}, "then": {"title": "Bun CLI Segment", "description": "https://ohmyposh.dev/docs/segments/cli/bun", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a Bun workspace", "default": ["bun.lockb", "bun.lock"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "carbonintensity"}}}, "then": {"title": "Carbon Intensity Segment", "description": "Displays the actual and forecast carbon intensity in gCO2/kWh using the Carbon Intensity API", "properties": {"properties": {"properties": {"http_timeout": {"$ref": "#/definitions/http_timeout"}}}}}}, {"if": {"properties": {"type": {"const": "cds"}}}, "then": {"title": "CDS (SAP CAP) segment", "description": "https://ohmyposh.dev/docs/segments/cloud/cds", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if the current directory is a CDS project", "default": [".cdsrc.json", ".cdsrc-private.json", "*.cds"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "cf"}}}, "then": {"title": "Clound Foundry CLI segment", "description": "https://ohmyposh.dev/docs/segments/cloud/cf", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if the current directory is a Cloud Foundry project", "default": ["manifest.yml", "mta.yaml"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "cftarget"}}}, "then": {"title": "Clound Foundry Target segment", "description": "https://ohmyposh.dev/docs/segments/cloud/cftarget", "properties": {"properties": {"properties": {"display_mode": {"type": "string", "title": "Display Mode", "description": "Determines whether the segment is displayed always or only if a file matching the extensions are present in the current folder", "enum": ["always", "files"], "default": "always"}}}}}}, {"if": {"properties": {"type": {"const": "cmake"}}}, "then": {"title": "Cmake Segment", "description": "https://ohmyposh.dev/docs/segments/cli/cmake", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a CMake workspace", "default": ["*.cmake", "CMakeLists.txt"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "command"}}}, "then": {"title": "Command Segment", "description": "https://ohmyposh.dev/docs/segments/system/command", "properties": {"properties": {"properties": {"shell": {"type": "string", "title": "Shell", "description": "The shell in which to run the command in. Uses shell -c command under the hood", "default": "bash"}, "command": {"type": "string", "title": "Command", "description": "the command(s) to run", "default": ""}, "script": {"type": "string", "title": "<PERSON><PERSON><PERSON>", "description": "A script to run", "default": ""}}}}}}, {"if": {"properties": {"type": {"const": "connection"}}}, "then": {"title": "Connection Segment", "description": "https://ohmyposh.dev/docs/segments/system/connection", "properties": {"properties": {"properties": {"type": {"type": "string", "title": "Connection type", "description": "The connection type to display", "enum": ["ethernet", "wifi", "cellular", "bluetooth"], "default": "wifi|ethernet"}, "unit": {"type": "string", "title": "Transfer speed unit", "enum": ["none", "b", "bps", "K", "Kbps", "M", "Mbps", "G", "Gbps", "T", "Tbps"], "default": "none"}}}}}}, {"if": {"properties": {"type": {"const": "crystal"}}}, "then": {"title": "Crystal Segment", "description": "https://ohmyposh.dev/docs/segments/languages/crystal", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a Crystal workspace", "default": ["*.cr", "shard.yml"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "dart"}}}, "then": {"title": "Dart Segment", "description": "https://ohmyposh.dev/docs/segments/languages/dart", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a Dart workspace", "default": ["*.dart", "pubspec.yaml", "pubspec.yml", "pubspec.lock"], "items": {"type": "string"}}, "folders": {"type": "array", "title": "Folders", "description": "The folders to look for when determining if a folder is a Dart workspace", "default": [".dart_tool"], "items": {"type": "string"}}}}}}}, {"if": {"properties": {"type": {"const": "deno"}}}, "then": {"title": "Deno CLI Segment", "description": "https://ohmyposh.dev/docs/segments/cli/deno", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a Deno workspace", "default": ["*.js", "*.ts", "deno.json"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"style": {"const": "diamond"}}}, "then": {"properties": {"leading_diamond": {"type": "string", "title": "Leading diamond", "description": "https://ohmyposh.dev/docs/configuration/segment#leading-diamond", "default": ""}, "trailing_diamond": {"type": "string", "title": "Trailing diamond", "description": "https://ohmyposh.dev/docs/configuration/segment#trailing-diamond", "default": ""}}}}, {"if": {"properties": {"type": {"const": "docker"}}}, "then": {"title": "Docker Segment", "description": "https://ohmyposh.dev/docs/segments/cli/docker", "properties": {"properties": {"properties": {"fetch_context": {"type": "boolean", "title": "Fetch Context", "description": "Fetch the Docker context", "default": true}, "display_mode": {"$ref": "#/definitions/display_mode"}}}}}}, {"if": {"properties": {"type": {"const": "dotnet"}}}, "then": {"title": "Dotnet Segment", "description": "https://ohmyposh.dev/docs/segments/languages/dotnet", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a .NET workspace", "default": ["*.cs", "*.csx", "*.vb", "*.fs", "*.fsx", "*.sln", "*.slnf", "*.c<PERSON><PERSON>j", "*.fsproj", "*.vbp<PERSON>j", "global.json"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "elixir"}}}, "then": {"title": "Elixir Segment", "description": "https://ohmyposh.dev/docs/segments/languages/elixir", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a Elixir workspace", "default": ["*.ex", "*.exs"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "executiontime"}}}, "then": {"title": "Displays the execution time of the previously executed command", "description": "https://ohmyposh.dev/docs/segments/system/executiontime", "properties": {"properties": {"properties": {"always_enabled": {"type": "boolean", "title": "Always Enabled", "description": "Always show the duration", "default": false}, "threshold": {"type": "number", "title": "<PERSON><PERSON><PERSON><PERSON>", "description": "minimum duration (milliseconds) required to enable this segment", "default": 500}, "style": {"type": "string", "title": "Style", "description": "The style in which the time will be displayed", "enum": ["austin", "roundrock", "dallas", "galveston", "galvestonms", "houston", "<PERSON><PERSON><PERSON>", "round", "lucky7"], "default": "austin"}}}}}}, {"if": {"properties": {"type": {"const": "firebase"}}}, "then": {"title": "Firebase Segment", "description": "https://ohmyposh.dev/docs/segments/cli/firebase"}}, {"if": {"properties": {"type": {"const": "flutter"}}}, "then": {"title": "Flutter Segment", "description": "https://ohmyposh.dev/docs/segments/cli/flutter", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a Flutter workspace", "default": ["*.dart", "pubspec.yaml", "pubspec.yml", "pubspec.lock"], "items": {"type": "string"}}, "folders": {"type": "array", "title": "Folders", "description": "The folders to look for when determining if a folder is a Flutter workspace", "default": [".dart_tool"], "items": {"type": "string"}}}}}}}, {"if": {"properties": {"type": {"const": "fortran"}}}, "then": {"title": "Fortran Segment", "description": "https://ohmyposh.dev/docs/segments/languages/fortran", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a Fortran workspace", "default": ["fpm.toml", "*.f", "*.for", "*.fpp", "*.f77", "*.f90", "*.f95", "*.f03", "*.f08", "*.F", "*.FOR", "*.FPP", "*.F77", "*.F90", "*.F95", "*.F03", "*.F08"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "fossil"}}}, "then": {"title": "Fossil Segment", "description": "https://ohmyposh.dev/docs/segments/scm/fossil", "properties": {"properties": {"properties": {"native_fallback": {"$ref": "#/definitions/native_fallback"}}}}}}, {"if": {"properties": {"type": {"const": "gcp"}}}, "then": {"title": "GCP Segment", "description": "https://ohmyposh.dev/docs/segments/cloud/gcp"}}, {"if": {"properties": {"type": {"const": "git"}}}, "then": {"title": "Git Segment", "description": "https://ohmyposh.dev/docs/segments/scm/git", "properties": {"properties": {"properties": {"fetch_status": {"type": "boolean", "title": "Display Status", "description": "Display the local changes or not", "default": true}, "fetch_stash_count": {"type": "boolean", "title": "<PERSON><PERSON>lay Stash Count", "description": "Display the stash count or not", "default": false}, "fetch_worktree_count": {"type": "boolean", "title": "Display Worktree Count", "description": "Display the worktree count or not", "default": false}, "fetch_upstream_icon": {"type": "boolean", "title": "Display Upstream Icon", "description": "Display upstream icon or not", "default": false}, "fetch_bare_info": {"type": "boolean", "title": "Fetch info when in a bare repo", "description": "Fetch info when in a bare repo or not", "default": false}, "branch_icon": {"type": "string", "title": "Branch Icon", "description": "The icon to use in front of the git branch name", "default": " "}, "branch_identical_icon": {"type": "string", "title": "Branch Identical Icon", "description": "The icon to display when remote and local are identical", "default": "≡"}, "branch_ahead_icon": {"type": "string", "title": "Branch Ahead Icon", "description": "The icon to display when the local branch is ahead of its remote", "default": "↑"}, "branch_behind_icon": {"type": "string", "title": "Branch Behind Icon", "description": "The icon to display when the local branch is behind its remote", "default": "↓"}, "branch_gone_icon": {"type": "string", "title": "Branch Gone Icon", "description": "The icon to display when there's no remote branch", "default": "≢"}, "commit_icon": {"type": "string", "title": "Commit Icon", "description": "Icon/text to display before the commit context (detached HEAD)", "default": ""}, "tag_icon": {"type": "string", "title": "Tag Icon", "description": "Icon/text to display before the tag context", "default": ""}, "rebase_icon": {"type": "string", "title": "Rebase Icon", "description": "Icon/text to display before the context when in a rebase", "default": ""}, "cherry_pick_icon": {"type": "string", "title": "Cherry-pick <PERSON><PERSON>", "description": "Icon/text to display before the context when doing a cherry-pick", "default": ""}, "revert_icon": {"type": "string", "title": "<PERSON><PERSON>", "description": "Icon/text to display before the context when doing a revert", "default": ""}, "merge_icon": {"type": "string", "title": "<PERSON><PERSON>", "description": "Icon/text to display before the merge context", "default": ""}, "no_commits_icon": {"type": "string", "title": "No Commits Icon", "description": "Icon/text to display when there are no commits in the repo", "default": ""}, "github_icon": {"type": "string", "title": "Github Icon", "description": "Icon/text to display when the upstream is Github", "default": ""}, "gitlab_icon": {"type": "string", "title": "Gitlab Icon", "description": "Icon/text to display when the upstream is Gitlab", "default": ""}, "bitbucket_icon": {"type": "string", "title": "Bitbucket Icon", "description": "Icon/text to display when the upstream is Bitbucket", "default": ""}, "azure_devops_icon": {"type": "string", "title": "Azure DevOps Icon", "description": "Icon/text to display when the upstream is Azure DevOps", "default": ""}, "codecommit_icon": {"type": "string", "title": "CodeCommit Icon", "description": "Icon/text to display when the upstream is CodeCommit", "default": ""}, "codeberg_icon": {"type": "string", "title": "Codeberg Icon", "description": "Icon/text to display when the upstream is Codeberg", "default": ""}, "git_icon": {"type": "string", "title": "Git Icon", "description": "Icon/text to display when the upstream is not known/mapped", "default": ""}, "untracked_modes": {"type": "object", "title": "Untracked files mode", "description": "Set the untracked files mode for a repository", "default": {}}, "ignore_submodules": {"type": "object", "title": "Ignore submodules", "description": "Ignore changes to submodules when looking for changes", "default": {}}, "ignore_status": {"type": "array", "title": "Ignore fetching status in these repo's", "description": "Ignore fetching status for certain repo's, uses the same logic as the exclude_folders property", "default": [], "items": {"type": "string"}}, "fetch_user": {"type": "boolean", "title": "Fetch the user", "description": "Fetch the current configured user for the repository", "default": false}, "status_formats": {"$ref": "#/definitions/status_formats"}, "upstream_icons": {"type": "object", "title": "Status string formats", "description": "a key, value map representing the remote URL (or a part of that URL) and icon to use in case the upstream URL contains the key. These get precedence over the standard icons", "default": {}}, "mapped_branches": {"$ref": "#/definitions/mapped_branches"}, "branch_template": {"$ref": "#/definitions/branch_template"}, "native_fallback": {"$ref": "#/definitions/native_fallback"}}}}}}, {"if": {"properties": {"type": {"const": "gitversion"}}}, "then": {"title": "Display GitVersion segment", "description": "https://ohmyposh.dev/docs/segments/cli/gitversion"}}, {"if": {"properties": {"type": {"const": "go"}}}, "then": {"title": "Golang Segment", "description": "https://ohmyposh.dev/docs/segments/languages/golang", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "parse_mod_file": {"type": "boolean", "title": "Parse go.mod file", "description": "Parse go.mod file instead of calling out to go to improve performance.", "default": false}, "parse_go_work_file": {"type": "boolean", "title": "Parse go.work file", "description": "Parse go.work file instead of calling out to go to improve performance.", "default": false}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a Go workspace", "default": ["*.go", "go.mod"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "haskell"}}}, "then": {"title": "Haskell Segment", "description": "https://ohmyposh.dev/docs/segments/languages/haskell", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "stack_ghc_mode": {"type": "string", "title": "Use Stack GHC", "description": "Get the GHC version used by Stack. Will decrease performance. Boolean indicating whether stack ghc was used available in template as .StackGhc", "enum": ["always", "package", "never"], "default": "never"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if the current directory is a Haskell project", "default": ["*.hs", "*.lhs", "stack.yaml", "package.yaml", "*.cabal", "cabal.project"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "helm"}}}, "then": {"title": "Helm segment", "description": "https://ohmyposh.dev/docs/segments/cli/helm", "properties": {"properties": {"properties": {"display_mode": {"$ref": "#/definitions/display_mode"}}}}}}, {"if": {"properties": {"type": {"const": "http"}}}, "then": {"title": "HTTP segment", "description": "https://ohmyposh.dev/docs/segments/web/http", "properties": {"properties": {"properties": {"url": {"type": "string", "title": "URL", "description": "The HTTP URL you want to call, supports templates", "default": ""}, "method": {"type": "string", "title": "HTTP Method", "description": "The HTTP method to use", "enum": ["GET", "POST"]}}}}}}, {"if": {"properties": {"type": {"const": "ipify"}}}, "then": {"title": "Display your external IP Address", "description": "https://ohmyposh.dev/docs/segments/web/ipify", "properties": {"properties": {"properties": {"url": {"type": "string", "title": "URL", "description": "The Ipify API URL", "default": "https://api.ipify.org"}, "http_timeout": {"$ref": "#/definitions/http_timeout"}}}}}}, {"if": {"properties": {"type": {"const": "java"}}}, "then": {"title": "Java Segment", "description": "https://ohmyposh.dev/docs/segments/languages/java", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a Java workspace", "default": ["pom.xml", "build.gradle.kts", "build.sbt", ".java-version", ".deps.edn", "project.clj", "build.boot", "*.java", "*.class", "*.gradle", "*.jar", "*.clj", "*.cljc"]}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "jujutsu"}}}, "then": {"title": "Jujutsu Segment", "description": "https://ohmyposh.dev/docs/segments/scm/jujutsu", "properties": {"properties": {"properties": {"fetch_status": {"type": "boolean", "title": "Display Status", "description": "Display the changes in the working copy", "default": false}, "status_formats": {"$ref": "#/definitions/status_formats"}, "native_fallback": {"$ref": "#/definitions/native_fallback"}}}}}}, {"if": {"properties": {"type": {"const": "julia"}}}, "then": {"title": "Julia Segment", "description": "https://ohmyposh.dev/docs/segments/languages/julia", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a Julia workspace", "default": ["*.jl"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "kotlin"}}}, "then": {"title": "<PERSON><PERSON><PERSON> Segment", "description": "https://ohmyposh.dev/docs/segments/languages/kotlin", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if the current directory is a Kotlin project", "default": ["*.kt", "*.kts", "*.ktm"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "kubectl"}}}, "then": {"title": "Kubectl Segment", "description": "https://ohmyposh.dev/docs/segments/cli/kubectl", "properties": {"properties": {"properties": {"display_error": {"type": "boolean", "title": "<PERSON><PERSON><PERSON>rror", "description": "Show the error context when failing to retrieve the kubectl information", "default": false}, "parse_kubeconfig": {"type": "boolean", "title": "Parse kubeconfig", "description": "Parse kubeconfig files instead of calling out to kubectl to improve performance.", "default": true}, "context_aliases": {"type": "object", "title": "Context aliases", "description": "Custom context names.", "default": {}}}}}}}, {"if": {"properties": {"type": {"const": "lastfm"}}}, "then": {"title": "LastFM Segment", "description": "https://ohmyposh.dev/docs/segments/music/lastfm", "properties": {"properties": {"properties": {"playing_icon": {"type": "string", "title": "User Info Separator", "description": "Text/icon to show when playing", "default": ""}, "stopped_icon": {"type": "string", "title": "SSH Icon", "description": "Text/icon to show when stopped", "default": ""}, "api_key": {"type": "string", "title": "API key", "description": "The API key used for the API call (Required)", "default": "."}, "username": {"type": "string", "title": "username", "description": "The username used for the API call (Required)", "default": "."}, "http_timeout": {"$ref": "#/definitions/http_timeout"}}}}}}, {"if": {"properties": {"type": {"const": "lua"}}}, "then": {"title": "Lua Segment", "description": "https://ohmyposh.dev/docs/segments/languages/lua", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "preferred_executable": {"type": "string", "title": "Preferred Executable", "description": "The preferred executable to use when fetching the version.", "enum": ["lua", "lua<PERSON>"], "default": "lua"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if the current directory is a Lua project", "default": ["*.lua", "*.rockspec"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "mercurial"}}}, "then": {"title": "Mercurial Segment", "description": "https://ohmyposh.dev/docs/segments/scm/mercurial", "properties": {"properties": {"properties": {"fetch_status": {"type": "boolean", "title": "Display Status", "description": "Display the local changes or not", "default": false}, "status_formats": {"$ref": "#/definitions/status_formats"}, "native_fallback": {"$ref": "#/definitions/native_fallback"}}}}}}, {"if": {"properties": {"type": {"const": "mojo"}}}, "then": {"title": "<PERSON><PERSON> Segment", "description": "https://ohmyposh.dev/docs/segments/languages/mojo", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_virtual_env": {"type": "boolean", "title": "Fetch Virtual Env", "description": "Fetch the name of the virtualenv or not", "default": true}, "display_default": {"type": "boolean", "title": "<PERSON><PERSON><PERSON>", "description": "Show the name of the virtualenv when it's default", "default": true}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a Mojo workspace", "default": ["*.🔥", "*.mojo", "mojoproject.toml"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "nightscout"}}}, "then": {"title": "Display Nightscout segment", "description": "https://ohmyposh.dev/docs/segments/health/nightscout", "properties": {"properties": {"properties": {"url": {"type": "string", "title": "URL", "description": "The URL to the Nightscout API", "default": ""}, "http_timeout": {"type": "integer", "title": "Http request timeout", "description": "Milliseconds to use for http request timeouts", "default": 500}, "headers": {"type": "object", "title": "Headers", "description": "A key, value map of Head<PERSON> to send with the request", "default": {}}}}}}}, {"if": {"properties": {"type": {"const": "nim"}}}, "then": {"title": "Nim Segment", "description": "https://ohmyposh.dev/docs/segments/languages/nim", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a Nim workspace", "default": ["*.nim", "*.nims"]}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "nix-shell"}}}, "then": {"title": "Nix Shell", "description": "https://ohmyposh.dev/docs/segments/cli/nix-shell"}}, {"if": {"properties": {"type": {"const": "node"}}}, "then": {"title": "Node Segment", "description": "https://ohmyposh.dev/docs/segments/languages/node", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "fetch_package_manager": {"type": "boolean", "title": "Fetch Display Package Manager", "description": "Assigns the Yarn or NPM icon to .PackageManagerIcon", "default": false}, "yarn_icon": {"type": "string", "title": "Yarn Icon", "description": "Icon/text to use for Yarn", "default": "B"}, "npm_icon": {"type": "string", "title": "NPM Icon", "description": "Icon/text to use for NPM", "default": ""}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a Node workspace", "default": ["*.js", "*.ts", "package.json", ".nvmrc", "pnpm-workspace.yaml", ".pnpmfile.cjs", ".vue"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "npm"}}}, "then": {"title": "NPM Segment", "description": "https://ohmyposh.dev/docs/segments/cli/npm", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is an NPM workspace", "default": ["package.json", "package-lock.json"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "nx"}}}, "then": {"title": "Nx Segment", "description": "https://ohmyposh.dev/docs/segments/cli/nx", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if the current directory is an Nx project", "default": ["workspace.json", "nx.json"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "ocaml"}}}, "then": {"title": "OCaml Segment", "description": "https://ohmyposh.dev/docs/segments/languages/ocaml", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if the current directory is an OCaml project", "default": ["*.ml", "*.mli", "dune", "dune-project", "dune-workspace"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "os"}}}, "then": {"title": "Operating System Segment", "description": "https://ohmyposh.dev/docs/segments/system/os", "properties": {"properties": {"properties": {"macos": {"type": "string", "title": "MacOS Icon", "description": "Icon/text to use for macOS", "default": ""}, "linux": {"type": "string", "title": "Linux Icon", "description": "Icon/text to use for Linux", "default": ""}, "windows": {"type": "string", "title": "Windows Icon", "description": "Icon/text to use for Windows", "default": ""}, "display_distro_name": {"type": "boolean", "title": "Display Distro Name", "description": "Display the distro name or icon or not", "default": false}, "alpine": {"type": "string", "title": "Alpine Icon", "description": "The icon to use for Alpine", "default": ""}, "aosc": {"type": "string", "title": "Aosc Icon", "description": "The icon to use for Aosc", "default": ""}, "arch": {"type": "string", "title": "Arch Icon", "description": "The icon to use for Arch", "default": ""}, "centos": {"type": "string", "title": "Centos I<PERSON>", "description": "The icon to use for <PERSON><PERSON><PERSON>", "default": ""}, "coreos": {"type": "string", "title": "Coreos Icon", "description": "The icon to use for Coreos", "default": ""}, "debian": {"type": "string", "title": "Debian Icon", "description": "The icon to use for Debian", "default": ""}, "devuan": {"type": "string", "title": "<PERSON><PERSON>", "description": "The icon to use for <PERSON>uan", "default": ""}, "raspbian": {"type": "string", "title": "Raspbian Icon", "description": "The icon to use for Raspbian", "default": ""}, "elementary": {"type": "string", "title": "Elementary Icon", "description": "The icon to use for Elementary", "default": ""}, "fedora": {"type": "string", "title": "Fedora <PERSON>", "description": "The icon to use for Fed<PERSON>", "default": ""}, "gentoo": {"type": "string", "title": "Gentoo Icon", "description": "The icon to use for Gentoo", "default": ""}, "mageia": {"type": "string", "title": "<PERSON><PERSON><PERSON>", "description": "The icon to use for Mageia", "default": ""}, "manjaro": {"type": "string", "title": "Manjaro <PERSON>", "description": "The icon to use for Manjaro", "default": ""}, "mint": {"type": "string", "title": "Mint Icon", "description": "The icon to use for Mint", "default": ""}, "nixos": {"type": "string", "title": "Nixos Icon", "description": "The icon to use for Nix<PERSON>", "default": ""}, "opensuse": {"type": "string", "title": "Opensuse Icon", "description": "The icon to use for Opensuse", "default": ""}, "redhat": {"type": "string", "title": "Redhat Icon", "description": "The icon to use for Redhat", "default": ""}, "sabayon": {"type": "string", "title": "Sabayon Icon", "description": "The icon to use for Sabayon", "default": ""}, "slackware": {"type": "string", "title": "Slackware Icon", "description": "The icon to use for Slackware", "default": ""}, "ubuntu": {"type": "string", "title": "Ubuntu Icon", "description": "The icon to use for Ubuntu", "default": ""}, "rocky": {"type": "string", "title": "Rocky Icon", "description": "The icon to use for <PERSON>", "default": ""}, "alma": {"type": "string", "title": "Alma Icon", "description": "The icon to use for Alma", "default": ""}, "android": {"type": "string", "title": "Android Icon", "description": "The icon to use for Alma", "default": ""}}}}}}, {"if": {"properties": {"type": {"const": "owm"}}}, "then": {"title": "Open Weather Map Segment", "description": "Displays the current weather from the Open Weather Map system", "properties": {"properties": {"properties": {"api_key": {"type": "string", "title": "API key", "description": "The API key used for the api call (Required)", "default": "."}, "location": {"type": "string", "title": "location", "description": "Location to use for the API call interpreted only if valid coordinates aren't given. Formatted as <City>,<STATE>,<COUNTRY_CODE>. City name, state code and country code divided by comma. Please, refer to ISO 3166 for the state codes or country codes.", "default": "De <PERSON>,NL"}, "units": {"type": "string", "title": "units", "description": "Units of measurement. Available values are standard (kelvin), metric (celsius), and imperial (fahrenheit). Default is standard", "default": "standard", "enum": ["standard", "metric", "imperial"]}, "http_timeout": {"$ref": "#/definitions/http_timeout"}}}}}}, {"if": {"properties": {"type": {"const": "path"}}}, "then": {"title": "Path Segment", "description": "https://ohmyposh.dev/docs/segments/system/path", "properties": {"properties": {"properties": {"folder_separator_icon": {"type": "string", "title": "Folder Separator Icon", "description": "The symbol to use as a separator between folders", "default": "/"}, "folder_separator_template": {"type": "string", "title": "Folder Separator Template", "description": "the path which is split will be separated by this template", "pattern": ""}, "home_icon": {"type": "string", "title": "Home Icon", "description": "The icon to display when at $HOME", "default": "~"}, "folder_icon": {"type": "string", "title": "Folder Icon", "description": "The icon to use as a folder indication", "default": ".."}, "windows_registry_icon": {"type": "string", "title": "Windows Registry Icon", "description": "The icon to display when in the Windows registry", "default": ""}, "style": {"type": "string", "title": "The Path Style", "description": "How to display the current path", "enum": ["agnoster", "agnoster_full", "agnoster_short", "agnoster_left", "short", "full", "folder", "mixed", "letter", "unique", "powerlevel", "fish"], "default": "agnoster"}, "mapped_locations": {"type": "object", "title": "Mapped Locations", "description": "Custom glyph/text for specific paths", "default": {}}, "max_depth": {"type": "integer", "title": "Maximum Depth", "description": "Maximum path depth to display without shortening", "default": 1}, "max_width": {"type": ["integer", "string"], "title": "Maximum Width", "description": "Maximum path width to display for powerlevel style", "default": 0}, "mapped_locations_enabled": {"type": "boolean", "title": "Enable the Mapped Locations feature", "description": "Replace known locations in the path with the replacements before applying the style.", "default": true}, "mixed_threshold": {"type": "integer", "title": "Mixed threshold", "description": "The maximum length of a path segment that will be displayed when using mixed style.", "default": 4}, "hide_root_location": {"type": "boolean", "title": "Hide the root location", "description": "Hides the root location, when using agnoster_short style, if it doesn't fit in the last max_depth folders.", "default": false}, "cycle": {"type": "array", "title": "Color overrides to use to cycle through and color the path per folder", "items": {"type": "string"}}, "cycle_folder_separator": {"type": "boolean", "title": "Cycle the folder_separator_icon", "description": "Colorize the folder_separator_icon as well when using a cycle.", "default": false}, "folder_format": {"type": "string", "title": "The folder format", "description": "Golang string format to apply to the folder name", "pattern": "%s"}, "edge_format": {"type": "string", "title": "The format to use on the start and end folder", "description": "Golang string format to apply to the start and end folder", "pattern": "%s"}, "left_format": {"type": "string", "title": "The format to use on first folder of the path", "description": "Will default to whatever edge_format is set to", "pattern": "%s"}, "right_format": {"type": "string", "title": "The format to use on the last folder of the path", "description": "Will default to whatever edge_format is set to", "pattern": "%s"}, "gitdir_format": {"type": "string", "title": "The format to use on a git root directory", "description": "Golang string format to apply to the .git folder", "default": ""}, "display_cygpath": {"type": "boolean", "title": "Display the Cygwin (Linux) style path", "description": "Display the Cygwin (Linux) style path using cygpath -u $PWD.", "default": false}, "dir_length": {"type": "integer", "title": "Directory Length", "description": "The length of the directory name to display in fish style.", "default": 1}, "full_length_dirs": {"type": "integer", "title": "Full Length Dirs", "description": "Indicates how many full length directory names should be displayed in fish style.", "default": 1}}}}}}, {"if": {"properties": {"type": {"const": "perl"}}}, "then": {"title": "Perl Segment", "description": "https://ohmyposh.dev/docs/segments/languages/perl", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a Perl workspace", "default": [".perl-version", "*.pl", "*.pm", "*.t"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "php"}}}, "then": {"title": "PHP Segment", "description": "https://ohmyposh.dev/docs/segments/languages/php", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a PHP workspace", "default": ["*.php", "composer.json", "composer.lock", ".php-version"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "plastic"}}}, "then": {"title": "Plastic SCM Segment", "description": "https://ohmyposh.dev/docs/segments/scm/plastic", "properties": {"properties": {"properties": {"fetch_status": {"type": "boolean", "title": "Display Status", "description": "Display the local changes or not", "default": false}, "status_formats": {"$ref": "#/definitions/status_formats"}, "branch_icon": {"type": "string", "title": "Branch Icon", "description": "The icon to use in front of the selector branch name", "default": " "}, "commit_icon": {"type": "string", "title": "Commit Icon", "description": "Icon/text to display before the selector changeset", "default": ""}, "tag_icon": {"type": "string", "title": "Tag Icon", "description": "Icon/text to display before the seletor label", "default": ""}, "branch_template": {"$ref": "#/definitions/branch_template"}, "native_fallback": {"$ref": "#/definitions/native_fallback"}, "mapped_branches": {"$ref": "#/definitions/mapped_branches"}}}}}}, {"if": {"properties": {"type": {"const": "pnpm"}}}, "then": {"title": "PNPM Segment", "description": "https://ohmyposh.dev/docs/segments/cli/pnpm", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is an PNPM workspace", "default": ["package.json", "pnpm-lock.yaml"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"style": {"const": "powerline"}}}, "then": {"properties": {"powerline_symbol": {"type": "string", "title": "Powerline Symbol", "description": "https://ohmyposh.dev/docs/configuration/segment#powerline-symbol", "default": ""}, "leading_powerline_symbol": {"type": "string", "title": "Leading Powerline Symbol", "description": "https://ohmyposh.dev/docs/configuration/segment#powerline-symbol", "default": ""}, "invert_powerline": {"type": "boolean", "title": "Flip the Powerline symbol vertically", "description": "https://ohmyposh.dev/docs/configuration/segment#invert-powerline", "default": false}}}}, {"if": {"properties": {"type": {"const": "project"}}}, "then": {"title": "Project Segment", "description": "https://ohmyposh.dev/docs/segments/system/project", "properties": {"properties": {"properties": {"always_enabled": {"type": "boolean", "title": "Always Enabled", "description": "Always show the segment", "default": false}}}}}}, {"if": {"properties": {"type": {"const": "pulumi"}}}, "then": {"title": "Pulumi Segment", "description": "https://ohmyposh.dev/docs/segments/cloud/pulumi", "properties": {"properties": {"properties": {"fetch_stack": {"type": "boolean", "title": "<PERSON><PERSON>ack", "description": "Fetch the current pulumi stack or not", "default": false}, "fetch_about": {"type": "boolean", "title": "Fetch About", "description": "Fetch the URL and user for the current stack", "default": false}}}}}}, {"if": {"properties": {"type": {"const": "python"}}}, "then": {"title": "Python Segment", "description": "https://ohmyposh.dev/docs/segments/languages/python", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_virtual_env": {"type": "boolean", "title": "Fetch Virtual Env", "description": "Fetch the name of the virtualenv or not", "default": true}, "display_default": {"type": "boolean", "title": "<PERSON><PERSON><PERSON>", "description": "Show the name of the virtualenv when it's default", "default": true}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a Python workspace", "default": ["*.py", "*.ipynb", "pyproject.toml", "venv.bak"], "items": {"type": "string"}}, "folders": {"type": "array", "title": "Folders", "description": "The folders to look for when determining if a folder is a Python workspace", "default": [".venv", "venv", "virtualenv", "venv-win", "pyenv-win"], "items": {"type": "string"}}, "folder_name_fallback": {"type": "boolean", "title": "Folder Name Fallback", "description": "Replace virtual environment names in default_venv_names list with parent folder name", "default": "true"}, "default_venv_names": {"type": "array", "title": "De<PERSON><PERSON>", "description": "Names to replace when folder_name_fallback is true", "default": [".venv", "venv"], "items": {"type": "string"}}}}}}}, {"if": {"properties": {"type": {"const": "quasar"}}}, "then": {"title": "Quasar Segment", "description": "https://ohmyposh.dev/docs/segments/cli/quasar", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "fetch_dependencies": {"type": "boolean", "title": "Fetch Dependencies", "description": "Fetch the vite and @quasar/app-vite dependency information or not", "default": true}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a Quasar workspace", "default": ["quasar.config", "quasar.config.js"]}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "r"}}}, "then": {"title": "R Segment", "description": "https://ohmyposh.dev/docs/segments/languages/r", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if the current directory is an R project", "default": ["*.R", "*.Rmd", "*.Rsx", "*.<PERSON>a", "*.Rd", "*.<PERSON><PERSON><PERSON><PERSON>", ".Rproj.user"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "react"}}}, "then": {"title": "React Segment", "description": "https://ohmyposh.dev/docs/segments/cli/react", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if the current directory is a React project", "default": ["package.json"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "root"}}}, "then": {"title": "Root Segment", "description": "https://ohmyposh.dev/docs/segments/system/root"}}, {"if": {"properties": {"type": {"const": "ruby"}}}, "then": {"title": "Ruby Segment", "description": "https://ohmyposh.dev/docs/segments/languages/ruby", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a Ruby workspace", "default": ["*.rb", "Rakefile", "Gem<PERSON>le"]}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "rust"}}}, "then": {"title": "Rust Segment", "description": "https://ohmyposh.dev/docs/segments/languages/rust", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a Rust workspace", "default": ["*.rs", "Cargo.toml", "Cargo.lock"]}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "sapling"}}}, "then": {"title": "Sapling Segment", "description": "https://ohmyposh.dev/docs/segments/scm/sapling", "properties": {"properties": {"properties": {"fetch_status": {"type": "boolean", "title": "Display Status", "description": "Display the local changes or not", "default": true}, "status_formats": {"$ref": "#/definitions/status_formats"}, "native_fallback": {"$ref": "#/definitions/native_fallback"}}}}}}, {"if": {"properties": {"type": {"const": "session"}}}, "then": {"title": "Session Segment", "description": "https://ohmyposh.dev/docs/segments/system/session", "properties": {"properties": {"properties": {"ssh_icon": {"type": "string", "title": "SSH Icon", "description": "Text/icon to display first when in an active SSH session", "default": ""}}}}}}, {"if": {"properties": {"type": {"const": "shell"}}}, "then": {"title": "Shell Segment", "description": "https://ohmyposh.dev/docs/segments/system/shell", "properties": {"properties": {"properties": {"custom_text": {"type": "object", "title": "Custom Text", "description": "Custom glyph/text for specific shells", "default": {}}}}}}}, {"if": {"properties": {"type": {"const": "sitecore"}}}, "then": {"title": "Sitecore Segment", "description": "https://ohmyposh.dev/docs/segments/cloud/sitecore", "properties": {"properties": {"properties": {"display_default": {"type": "boolean", "title": "<PERSON><PERSON><PERSON>", "description": "Display the segment or not when the Sitecore environment name matches `default`", "default": true}}}}}}, {"if": {"properties": {"type": {"const": "spotify"}}}, "then": {"title": "Spotify Segment", "description": "https://ohmyposh.dev/docs/segments/music/spotify", "properties": {"properties": {"properties": {"playing_icon": {"type": "string", "title": "Playing Icon", "description": "Text/icon to show when playing", "default": ""}, "paused_icon": {"type": "string", "title": "Paused Icon", "description": "Text/icon to show when paused", "default": ""}, "stopped_icon": {"type": "string", "title": "Stopped Icon", "description": "Text/icon to show when stopped", "default": ""}}}}}}, {"if": {"properties": {"type": {"const": "status"}}}, "then": {"title": "Status Segment", "description": "https://ohmyposh.dev/docs/segments/system/status", "properties": {"properties": {"properties": {"always_enabled": {"type": "boolean", "title": "Always Enabled", "description": "Always show the status", "default": false}, "status_template": {"type": "string", "title": "Status Template", "description": "The template to use for the status segment", "default": "|"}, "status_separator": {"type": "string", "title": "Status Separator", "description": "The separator to use between the status segments", "default": "|"}}}}}}, {"if": {"properties": {"type": {"const": "strava"}}}, "then": {"title": "Display training data from Strava", "description": "https://ohmyposh.dev/docs/segments/health/strava", "properties": {"properties": {"properties": {"url": {"type": "string", "title": "URL of API with Strava data", "description": "Url of your api provinding a Strava activity", "default": ""}, "ride_icon": {"type": "string", "title": "Ride icon", "description": "Alternative icon for this activity type", "default": ""}, "run_icon": {"type": "string", "title": "Run icon", "description": "Alternative icon for this activity type", "default": ""}, "skiing_icon": {"type": "string", "title": "Skiing icon", "description": "Alternative icon for this activity type", "default": ""}, "workout_icon": {"type": "string", "title": "Workout icon", "description": "Alternative icon for this activity type", "default": ""}, "unknown_activity_icon": {"type": "string", "title": "Fallback icon", "description": "Fallback icon for other activity types", "default": ""}, "http_timeout": {"$ref": "#/definitions/http_timeout"}, "access_token": {"$ref": "#/definitions/access_token"}, "refresh_token": {"$ref": "#/definitions/refresh_token"}, "expires_in": {"$ref": "#/definitions/expires_in"}}}}}}, {"if": {"properties": {"type": {"const": "svelte"}}}, "then": {"title": "Svelte Segment", "description": "https://ohmyposh.dev/docs/segments/cli/svelte", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if the current directory is a Svelte project", "default": ["package.json"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "svn"}}}, "then": {"title": "SVN Segment", "description": "https://ohmyposh.dev/docs/segments/scm/svn", "properties": {"properties": {"properties": {"fetch_status": {"type": "boolean", "title": "Display Status", "description": "Display the local changes or not", "default": true}, "status_formats": {"$ref": "#/definitions/status_formats"}, "native_fallback": {"$ref": "#/definitions/native_fallback"}}}}}}, {"if": {"properties": {"type": {"const": "swift"}}}, "then": {"title": "Swift Segment", "description": "https://ohmyposh.dev/docs/segments/languages/swift", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if the current directory is a Swift project", "default": ["*.swift", "*.SWIFT"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "sysinfo"}}}, "then": {"title": "Get sysinfo", "description": "https://ohmyposh.dev/docs/segments/system/sysinfo", "properties": {"properties": {"properties": {"precision": {"type": "integer", "title": "Precision", "description": "number of decimal places to show", "default": 2}}}}}}, {"if": {"properties": {"type": {"const": "talosctl"}}}, "then": {"title": "Talosctl Segment", "description": "https://ohmyposh.dev/docs/segments/cli/talosctl"}}, {"if": {"properties": {"type": {"const": "tauri"}}}, "then": {"title": "Tauri Segment", "description": "https://ohmyposh.dev/docs/segments/cli/tauri", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if the current directory is a Tauri project", "default": ["package.json"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "terraform"}}}, "then": {"title": "Terraform Segment", "description": "https://ohmyposh.dev/docs/segments/cli/terraform", "properties": {"properties": {"properties": {"fetch_version": {"type": "boolean", "title": "Fetch Version", "description": "Fetch the version number", "default": false}}}}}}, {"if": {"properties": {"type": {"const": "text"}}}, "then": {"title": "Text Segment", "description": "https://ohmyposh.dev/docs/segments/system/text"}}, {"if": {"properties": {"type": {"const": "time"}}}, "then": {"title": "Time Segment", "description": "https://ohmyposh.dev/docs/segments/system/time", "properties": {"properties": {"properties": {"time_format": {"type": "string", "title": "Time Format", "description": "Format to use, follows the golang standard: https://gobyexample.com/time-formatting-parsing", "default": "15:04:05"}}}}}}, {"if": {"properties": {"type": {"const": "ui5tooling"}}}, "then": {"title": "UI5 tooling CLI segment", "description": "https://ohmyposh.dev/docs/segments/cli/ui5tooling", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if the current directory is a UI5 project", "default": ["*ui5*.y*ml"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "umbraco"}}}, "then": {"title": "Umbraco Segment", "description": "https://ohmyposh.dev/docs/segments/cli/umbraco"}}, {"if": {"properties": {"type": {"const": "unity"}}}, "then": {"title": "Unity Segment", "description": "https://ohmyposh.dev/docs/segments/cli/unity", "properties": {"properties": {"properties": {"http_timeout": {"$ref": "#/definitions/http_timeout"}}}}}}, {"if": {"properties": {"type": {"const": "upgrade"}}}, "then": {"title": "Upgrade Segment", "description": "https://ohmyposh.dev/docs/segments/system/upgrade", "properties": {"properties": {"properties": {"cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}}}}}}, {"if": {"properties": {"type": {"const": "v"}}}, "then": {"title": "V Segment", "description": "https://ohmyposh.dev/docs/segments/languages/v", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if the current directory is a V project", "default": ["*.v"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "vala"}}}, "then": {"title": "Vala Segment", "description": "https://ohmyposh.dev/docs/segments/languages/vala", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if the current directory is a Vala project", "default": ["*.vala"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "wakatime"}}}, "then": {"title": "Wakatime", "description": "Displays the tracked time on wakatime.com", "properties": {"properties": {"properties": {"apikey": {"type": "string", "title": "apikey", "description": "The apikey used for the api call (Required)", "default": "."}, "http_timeout": {"$ref": "#/definitions/http_timeout"}}}}}}, {"if": {"properties": {"type": {"const": "winreg"}}}, "then": {"title": "Windows Registry Query", "description": "https://ohmyposh.dev/docs/segments/system/winreg", "properties": {"properties": {"properties": {"path": {"type": "string", "title": "Registry Path", "description": "The path to the registry key (case insensitive, must use backslashes).  Ending with \\ will retrieve \"(Default)\" key in that path.", "default": ""}, "fallback": {"type": "string", "title": "Fallback value", "description": "Value to display if registry value cannot be retrieved", "default": ""}}}}}}, {"if": {"properties": {"type": {"const": "withings"}}}, "then": {"title": "Display activity data from Withings", "description": "https://ohmyposh.dev/docs/segments/health/withings", "properties": {"properties": {"properties": {"http_timeout": {"$ref": "#/definitions/http_timeout"}, "access_token": {"$ref": "#/definitions/access_token"}, "refresh_token": {"$ref": "#/definitions/refresh_token"}, "expires_in": {"$ref": "#/definitions/expires_in"}}}}}}, {"if": {"properties": {"type": {"const": "xmake"}}}, "then": {"title": "XMake Segment", "description": "https://ohmyposh.dev/docs/segments/cli/xmake", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is a XMake workspace", "default": ["xmake.lua"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "yarn"}}}, "then": {"title": "Yarn Segment", "description": "https://ohmyposh.dev/docs/segments/cli/yarn", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if a folder is an Yarn workspace", "default": ["package.json", "yarn.lock"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}, {"if": {"properties": {"type": {"const": "ytm"}}}, "then": {"title": "YouTube Music Desktop App Segment", "description": "https://ohmyposh.dev/docs/segments/music/ytm", "properties": {"properties": {"properties": {"playing_icon": {"type": "string", "title": "Playing Icon", "description": "Text/icon to show when playing", "default": " "}, "paused_icon": {"type": "string", "title": "Paused Icon", "description": "Text/icon to show when paused", "default": " "}, "stopped_icon": {"type": "string", "title": "Stopped Icon", "description": "Text/icon to show when stopped", "default": " "}, "api_url": {"type": "string", "title": "API URL", "description": "The YTMDA Remote Control API URL", "default": "http://127.0.0.1:9863"}, "http_timeout": {"$ref": "#/definitions/http_timeout"}}}}}}, {"if": {"properties": {"type": {"const": "zig"}}}, "then": {"title": "Zig Segment", "description": "https://ohmyposh.dev/docs/segments/languages/zig", "properties": {"properties": {"properties": {"home_enabled": {"$ref": "#/definitions/home_enabled"}, "fetch_version": {"$ref": "#/definitions/fetch_version"}, "cache_duration": {"$ref": "#/definitions/cache_duration", "default": "none"}, "display_mode": {"$ref": "#/definitions/display_mode"}, "missing_command_text": {"$ref": "#/definitions/missing_command_text"}, "version_url_template": {"$ref": "#/definitions/version_url_template"}, "extensions": {"type": "array", "title": "Extensions", "description": "The extensions to look for when determining if the current directory is an zig project", "default": ["*.zig", "*.zon"], "items": {"type": "string"}}, "folders": {"$ref": "#/definitions/folders"}}}}}}]}}, "required": ["blocks"], "properties": {"final_space": {"type": "boolean", "title": "Final Space", "description": "https://ohmyposh.dev/docs/configuration/general#general-settings", "default": true}, "enable_cursor_positioning": {"type": "boolean", "title": "Enable <PERSON><PERSON><PERSON>", "description": "https://ohmyposh.dev/docs/configuration/general#general-settings", "default": false}, "shell_integration": {"type": "boolean", "title": "FTCS command marks for shell integration", "default": false}, "pwd": {"type": "string", "title": "Enable OSC99/7/51", "description": "https://ohmyposh.dev/docs/configuration/general#general-settings", "default": ""}, "upgrade": {"type": "object", "title": "Enable Upgrade Notice", "description": "https://ohmyposh.dev/docs/configuration/general#general-settings", "default": {"source": "cdn", "auto": false, "notice": false}, "properties": {"interval": {"$ref": "#/definitions/cache_duration"}, "source": {"type": "string", "enum": ["cdn", "github"], "default": "cdn"}, "auto": {"type": "boolean", "default": false}, "notice": {"type": "boolean", "default": false}}}, "patch_pwsh_bleed": {"type": "boolean", "title": "Patch PowerShell Color Bleed", "description": "https://ohmyposh.dev/docs/configuration/general#general-settings", "default": false}, "console_title_template": {"type": "string", "title": "Console Title Template", "description": "https://ohmyposh.dev/docs/configuration/title#console-title-template", "default": "{{ .Shell }} in {{ .Folder }}"}, "terminal_background": {"$ref": "#/definitions/color"}, "blocks": {"type": "array", "title": "Block array", "default": [], "description": "https://ohmyposh.dev/docs/configuration/general#blocks", "items": {"$ref": "#/definitions/block"}}, "tooltips": {"type": "array", "title": "Tooltip list, prompt elements to display based on context", "description": "https://ohmyposh.dev/docs/configuration/tooltips", "default": [], "items": {"allOf": [{"$ref": "#/definitions/segment"}], "properties": {"tips": {"type": "array", "title": "The commands for which you want the segment to show", "items": {"type": "string"}}}, "required": ["tips"]}}, "transient_prompt": {"$ref": "#/definitions/extra_prompt", "title": "Transient Prompt Setting", "description": "https://ohmyposh.dev/docs/configuration/transient", "anyOf": [{"properties": {"filler": {"$ref": "#/definitions/filler"}, "newline": {"type": "boolean", "title": "Newline", "description": "Add a newline before the prompt", "default": false}}}]}, "valid_line": {"$ref": "#/definitions/extra_prompt", "title": "Valid Line Setting (for PowerShell only)", "description": "https://ohmyposh.dev/docs/configuration/line-error"}, "error_line": {"$ref": "#/definitions/extra_prompt", "title": "Error Line Setting (for PowerShell only)", "description": "https://ohmyposh.dev/docs/configuration/line-error"}, "secondary_prompt": {"$ref": "#/definitions/extra_prompt", "title": "Secondary Prompt Setting", "description": "https://ohmyposh.dev/docs/configuration/secondary-prompt"}, "debug_prompt": {"$ref": "#/definitions/extra_prompt", "title": "Debug Prompt Setting (for PowerShell only)", "description": "https://ohmyposh.dev/docs/configuration/debug-prompt"}, "palette": {"type": "object", "title": "Palette", "description": "https://ohmyposh.dev/docs/configuration/colors#palette", "default": {}, "patternProperties": {".*": {"$ref": "#/definitions/color"}}}, "palettes": {"type": "object", "title": "Palettes", "description": "https://ohmyposh.dev/docs/configuration/colors#palettes", "default": {}, "properties": {"template": {"type": "string", "title": "Prompt Template"}, "list": {"type": "object", "title": "List of palettes", "patternProperties": {".*": {"$ref": "#/properties/palette"}}}}}, "cycle": {"type": "array", "title": "List of settings to cycle through segment by segment", "description": "https://ohmyposh.dev/docs/configuration/cycle", "default": [], "items": {"properties": {"foreground": {"$ref": "#/definitions/color"}, "background": {"$ref": "#/definitions/color"}}}}, "accent_color": {"title": "Accent color", "$ref": "#/definitions/color"}, "iterm_features": {"type": "array", "title": "The iTerm2 features to enable", "items": {"type": "string", "enum": ["prompt_mark", "current_dir", "remote_host"]}}, "var": {"type": "object", "title": "Config variables to use in templates (can be any value)", "description": "https://ohmyposh.dev/docs/configuration/templates#config-variables", "default": {}}, "maps": {"type": "object", "title": "Custom text mappings", "description": "https://ohmyposh.dev/docs/configuration/general#maps", "default": {}, "items": {"properties": {"user_name": {"$ref": "#/definitions/aliases"}, "host_name": {"$ref": "#/definitions/aliases"}, "shell_name": {"$ref": "#/definitions/aliases"}}}}, "async": {"type": "boolean", "title": "Async loading", "default": false}, "tooltips_action": {"type": "string", "title": "Tooltips action", "description": "https://ohmyposh.dev/docs/configuration/tooltips#tooltips-action", "enum": ["replace", "extend", "prepend"], "default": "replace"}}}